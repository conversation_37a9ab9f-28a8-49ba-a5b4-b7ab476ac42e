import { useState, useEffect } from "react";
import { Button } from "../ui/Button";
import { Card } from "../ui/Card";
import { PageHeader } from "../ui/PageHeader";
import PerformanceRadarChart from "../ui/PerformanceRadarChart";
import CreatePerformanceProfile from "./CreatePerformanceProfile";
import EditPerformanceProfile from "./EditPerformanceProfile";
import {
  PerformanceProfile,
  PerformanceCategory,
  LatestActiveGoalsResponse,
} from "../../types/api/performanceProfile.types";
import {
  getPerformanceProfiles,
  getLatestActiveGoals,
  deletePerformanceProfile,
} from "../../api/performanceProfile";
import { Plus, Calendar, Target, Trash2, Eye } from "lucide-react";
import { LoadingSpinner } from "../ui/LoadingSpinner";

const PerformanceProfiling = () => {
  const [profiles, setProfiles] = useState<PerformanceProfile[]>([]);
  const [latestGoals, setLatestGoals] =
    useState<LatestActiveGoalsResponse | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingProfileId, setEditingProfileId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [profilesData, latestGoalsData] = await Promise.all([
        getPerformanceProfiles(),
        getLatestActiveGoals(),
      ]);

      setProfiles(profilesData);
      setLatestGoals(latestGoalsData);
    } catch (error: any) {
      console.error("Error fetching performance data:", error);
      setError("Failed to load performance data");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleCreateSuccess = () => {
    setShowCreateForm(false);
    fetchData();
  };

  const handleEditSuccess = () => {
    setEditingProfileId(null);
    fetchData();
  };

  const handleEditProfile = (profileId: string) => {
    setEditingProfileId(profileId);
  };

  const handleDeleteProfile = async (profileId: string) => {
    if (!confirm("Are you sure you want to delete this performance profile?")) {
      return;
    }

    try {
      await deletePerformanceProfile(profileId);
      fetchData();
    } catch (error: any) {
      console.error("Error deleting profile:", error);
      setError("Failed to delete performance profile");
    }
  };

  if (loading) {
    return <LoadingSpinner />;
  }

  if (showCreateForm) {
    return (
      <CreatePerformanceProfile
        onSuccess={handleCreateSuccess}
        onCancel={() => setShowCreateForm(false)}
      />
    );
  }

  if (editingProfileId) {
    return (
      <EditPerformanceProfile
        profileId={editingProfileId}
        onSuccess={handleEditSuccess}
        onCancel={() => setEditingProfileId(null)}
      />
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <PageHeader
          title="Performance Profiling"
          description="Track your performance goals across different categories"
        />
        <Button
          onClick={() => setShowCreateForm(true)}
          className="flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>Create New Profile</span>
        </Button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {/* Radar Charts Section */}
      {latestGoals && (
        <div className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Current Performance Overview
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {Object.entries(PerformanceCategory).map(([, category]) => {
                const goalsByCategory = latestGoals.goalsByCategory || {};
                const categoryGoals = goalsByCategory[category] || [];

                return (
                  <Card key={category}>
                    <div className="p-6">
                      <PerformanceRadarChart
                        goals={categoryGoals}
                        category={category}
                        size={250}
                        showLabels={true}
                        showValues={true}
                        title={
                          category.charAt(0) + category.slice(1).toLowerCase()
                        }
                      />
                    </div>
                  </Card>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* Performance Profile Records Table */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-900">
          Performance Profile Records
        </h2>

        {profiles.length === 0 ? (
          <Card>
            <div className="p-8 text-center">
              <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No Performance Profiles Yet
              </h3>
              <p className="text-gray-500 mb-4">
                Create your first performance profile to start tracking your
                goals.
              </p>
              <Button
                onClick={() => setShowCreateForm(true)}
                className="flex items-center space-x-2"
              >
                <Plus className="h-4 w-4" />
                <span>Create Performance Profile</span>
              </Button>
            </div>
          </Card>
        ) : (
          <Card>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Timeline
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Goals Summary
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {profiles.map((profile) => {
                    const activeGoals = profile.goals.filter(
                      (goal) => goal.isActive
                    );
                    const goalsByCategory = activeGoals.reduce((acc, goal) => {
                      acc[goal.category] = (acc[goal.category] || 0) + 1;
                      return acc;
                    }, {} as Record<PerformanceCategory, number>);

                    return (
                      <tr key={profile.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4 text-gray-400" />
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {new Date(
                                  profile.startDate
                                ).toLocaleDateString()}{" "}
                                -{" "}
                                {new Date(
                                  profile.targetDate
                                ).toLocaleDateString()}
                              </div>
                              <div className="text-sm text-gray-500">
                                {Math.ceil(
                                  (new Date(profile.targetDate).getTime() -
                                    new Date().getTime()) /
                                    (1000 * 60 * 60 * 24)
                                )}{" "}
                                days remaining
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="space-y-1">
                            {Object.entries(goalsByCategory).map(
                              ([category, count]) => (
                                <div
                                  key={category}
                                  className="flex items-center space-x-2"
                                >
                                  <span className="text-xs px-2 py-1 bg-gray-100 rounded-full">
                                    {category}: {count} goals
                                  </span>
                                </div>
                              )
                            )}
                            {activeGoals.length === 0 && (
                              <span className="text-sm text-gray-500">
                                No active goals
                              </span>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(profile.createdAt).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditProfile(profile.id)}
                              className="flex items-center space-x-1"
                            >
                              <Eye className="h-3 w-3" />
                              <span>Edit</span>
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteProfile(profile.id)}
                              className="flex items-center space-x-1 text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-3 w-3" />
                              <span>Delete</span>
                            </Button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
};

export default PerformanceProfiling;
