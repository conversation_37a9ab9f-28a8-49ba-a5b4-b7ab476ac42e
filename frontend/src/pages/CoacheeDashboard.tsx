import { Route, Routes } from "react-router";
import { LoadingSpinner } from "../components/ui/LoadingSpinner";
import React from "react";
import CoacheeHome from "../components/coachee/CoacheeHome";
import CoacheeSidebar from "../components/coachee/CoacheeSidebar";
import Container from "../components/ui/Container";
import CoacheeAssignments from "../components/coachee/CoacheeAssignments";
import AssignmentSubmissionForm from "../components/coachee/AssignmentSubmissionForm";
import CreateIPSRecord from "../components/coachee/CreateIPSRecord";
import IPSVisualization from "../components/coachee/IPSVisualization";
import PerformanceProfiling from "../components/coachee/PerformanceProfiling";
import { CoacheeProvider } from "../context/CoacheeContext";

export default function CoacheeDashboard() {
  return (
    <CoacheeProvider>
      <Container>
        <div className="flex flex-row h-full">
          <CoacheeSidebar />
          <div className="w-full mx-auto overflow-y-auto px-4 sm:px-6 lg:px-8 py-8">
            <Routes>
              <Route
                path="/"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <CoacheeHome />
                  </React.Suspense>
                }
              />
              <Route
                path="/assignments"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <CoacheeAssignments />
                  </React.Suspense>
                }
              />
              <Route
                path="/assignments/:id"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <AssignmentSubmissionForm />
                  </React.Suspense>
                }
              />
              <Route
                path="/ips/create"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <CreateIPSRecord />
                  </React.Suspense>
                }
              />
              <Route
                path="/ips/visualization"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <IPSVisualization />
                  </React.Suspense>
                }
              />
              <Route
                path="/performance-profiling"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <PerformanceProfiling />
                  </React.Suspense>
                }
              />
              <Route
                path="/progress"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <div className="text-center py-12">
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        Progress Tracking
                      </h3>
                      <p className="text-gray-500">
                        Progress tracking feature coming soon!
                      </p>
                    </div>
                  </React.Suspense>
                }
              />
              <Route
                path="/profile"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <div className="text-center py-12">
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        Profile Settings
                      </h3>
                      <p className="text-gray-500">
                        Profile management feature coming soon!
                      </p>
                    </div>
                  </React.Suspense>
                }
              />
            </Routes>
          </div>
        </div>
      </Container>
    </CoacheeProvider>
  );
}
