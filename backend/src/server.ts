import express, { Request, Response, NextFunction } from "express";
import cors from "cors";
import { env } from "./config/env";
import authRoutes from "./routes/auth.routes";
import exerciseRoutes from "./routes/exercise.routes";
import visualizationRoutes from "./routes/visualization.routes";
import organizationRoutes from "./routes/organization.routes";
import assignmentRoutes from "./routes/assignment.routes";
import userRoutes from "./routes/user.routes";
import coachRoutes from "./routes/coach.routes";
import adminRoutes from "./routes/admin.routes";
import activityLogRoutes from "./routes/activityLog.routes";
import audioRoutes from "./routes/audio.routes";
import imageRoutes from "./routes/image.routes";
import evaluationRoutes from "./routes/evaluation.routes";
import ipsRoutes from "./routes/ips.routes";
import performanceProfileRoutes from "./routes/performanceProfile.routes";

const app = express();

// CORS Configuration - Simplified for debugging
app.use(
  cors({
    origin: [
      "http://localhost:5173",
      "http://localhost:3000",
      "https://myzone-mindset.netlify.app",
    ],
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With"],
    optionsSuccessStatus: 200,
  })
);

// Middleware
app.use(express.json()); // Parse JSON request bodies
app.use(express.urlencoded({ extended: true })); // Parse URL-encoded request bodies

// Health check endpoint
app.get("/", (req: Request, res: Response) => {
  res.send("MyZone Mindset Backend API is running!");
});

app.use("/api/auth", authRoutes);
app.use("/api/user", userRoutes); // Auth middleware is applied within the route file
app.use("/api/coach", coachRoutes); // Auth middleware is applied within the route file
app.use("/api/admin", adminRoutes); // Auth middleware is applied within the route file
app.use("/api/exercises", exerciseRoutes); // Auth middleware is applied within the route file
app.use("/api/visualizations", visualizationRoutes); // Auth middleware is applied within the route file
app.use("/api/organizations", organizationRoutes); // Auth middleware is applied within the route file
app.use("/api/assignments", assignmentRoutes); // Auth middleware is applied within the route file
app.use("/api/visualization-assignments", visualizationRoutes); // Auth middleware is applied within the route file
app.use("/api/activity-logs", activityLogRoutes); // Auth middleware is applied within the route file
app.use("/api/audio", audioRoutes); // Auth middleware is applied within the route file
app.use("/api/images", imageRoutes); // Auth middleware is applied within the route file
app.use("/api/evaluations", evaluationRoutes); // Auth middleware is applied within the route file
app.use("/api/ips", ipsRoutes); // Auth middleware is applied within the route file
app.use("/api/performance-profiles", performanceProfileRoutes); // Auth middleware is applied within the route file

// Basic Error Handling Middleware
// eslint-disable-next-line @typescript-eslint/no-unused-vars
app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
  console.error(err.stack);
  res.status(500).send("Something broke!");
});

// Start Server
app.listen(env.port, () => {
  console.log(`Server listening on port ${env.port}`);
});
