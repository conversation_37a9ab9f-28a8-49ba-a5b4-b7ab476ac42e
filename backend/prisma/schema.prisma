// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

//--------------------------------------
//          User and Auth Models
//--------------------------------------
model User {
  id               String    @id @default(uuid())
  email            String    @unique
  passwordHash     String    @map("password_hash")
  role             UserR<PERSON>  @default(COACHEE)
  firstName        String?   @map("first_name")
  lastName         String?   @map("last_name")
  resetToken       String?   @map("reset_token")
  resetTokenExpiry DateTime? @map("reset_token_expiry")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")

  // Relations
  createdExercises             Exercise[]                @relation("CreatedExercises")
  updatedExercises             Exercise[]                @relation("UpdatedExercises")
  createdVisualizations        Visualization[]           @relation("CreatedVisualizations")
  updatedVisualizations        Visualization[]           @relation("UpdatedVisualizations")
  assignedAssignments          Assignment[]              @relation("AssignedByCoach")
  receivedAssignments          Assignment[]              @relation("AssignedToCoachee")
  assignedVisAssignments       VisualizationAssignment[] @relation("VisAssignedByCoach")
  receivedVisAssignments       VisualizationAssignment[] @relation("VisAssignedToCoachee")
  coachRelationships           CoachCoachee[]            @relation("Coach")
  coacheeRelationships         CoachCoachee[]            @relation("Coachee")
  organizationMemberships      OrganizationCoachee[]     @relation("CoacheeOrgMembership")
  coachOrganizationMemberships OrganizationCoach[]       @relation("CoachOrgMembership")
  submittedSubmissions         AssignmentSubmission[]    @relation("SubmissionByCoachee")
  Organization                 Organization[]
  coachActivityLogs            ActivityLog[]             @relation("ActivityLogCoach")
  coacheeActivityLogs          ActivityLog[]             @relation("ActivityLogCoachee")
  createdEvaluations           CoacheeEvaluation[]       @relation("EvaluationCreatedBy")
  evaluationsReceived          CoacheeEvaluation[]       @relation("EvaluationCoachee")
  ipsRecords                   IdealPerformanceState[]   @relation("IPSCoachee")
  performanceProfiles          PerformanceProfile[]      @relation("ProfileCoachee")

  @@map("users")
}

enum UserRole {
  COACH
  COACHEE
  HR_ADMIN
  ADMIN // Added ADMIN for potential future use
}

//--------------------------------------
//       Organization Models
//--------------------------------------
model Organization {
  id        String   @id @default(uuid())
  name      String
  hrAdminId String?  @map("hr_admin_id") // User ID of the HR admin
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  hrAdmin            User?                 @relation(fields: [hrAdminId], references: [id], onDelete: SetNull)
  coacheeMemberships OrganizationCoachee[]
  coachMemberships   OrganizationCoach[]

  @@map("organizations")
}

// Join table for Organization and Coachee (User)
model OrganizationCoachee {
  organizationId String   @map("organization_id")
  coacheeId      String   @map("coachee_id") // User ID of the coachee
  joinedAt       DateTime @default(now()) @map("joined_at")

  // Relations
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  coachee      User         @relation("CoacheeOrgMembership", fields: [coacheeId], references: [id], onDelete: Cascade)

  @@id([organizationId, coacheeId])
  @@map("organization_coachees")
}

// Join table for Organization and Coach (User)
model OrganizationCoach {
  organizationId String   @map("organization_id")
  coachId        String   @map("coach_id") // User ID of the coach
  joinedAt       DateTime @default(now()) @map("joined_at")

  // Relations
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  coach        User         @relation("CoachOrgMembership", fields: [coachId], references: [id], onDelete: Cascade)

  @@id([organizationId, coachId])
  @@map("organization_coaches")
}

//--------------------------------------
//       Coach/Coachee Relationship
//--------------------------------------
// Join table for Coach and Coachee
model CoachCoachee {
  coachId    String   @map("coach_id")
  coacheeId  String   @map("coachee_id")
  coachNotes String?  @map("coach_notes") // Notes from the coach about the coachee
  assignedAt DateTime @default(now()) @map("assigned_at")

  // Relations
  coach   User @relation("Coach", fields: [coachId], references: [id], onDelete: Cascade)
  coachee User @relation("Coachee", fields: [coacheeId], references: [id], onDelete: Cascade)

  @@id([coachId, coacheeId])
  @@map("coach_coachees")
}

//--------------------------------------
//          Exercise Models
//--------------------------------------
model Exercise {
  id          String   @id @default(uuid())
  name        String
  description String?
  questions   Json     @db.Json // Storing the complex question structure as JSON (now includes TextBlocks)
  createdBy   String   @map("created_by") // User ID
  createdAt   DateTime @default(now()) @map("created_at")
  updatedBy   String   @map("updated_by") // User ID
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  creator     User         @relation("CreatedExercises", fields: [createdBy], references: [id])
  updater     User         @relation("UpdatedExercises", fields: [updatedBy], references: [id])
  assignments Assignment[]

  @@map("exercises")
}

model Assignment {
  id            String           @id @default(uuid())
  exerciseId    String           @map("exercise_id")
  coacheeId     String           @map("coachee_id") // User ID of the coachee
  coachId       String           @map("coach_id") // User ID of the coach who assigned it
  status        AssignmentStatus @default(PENDING)
  dueDate       DateTime?        @map("due_date")
  createdAt     DateTime         @default(now()) @map("created_at")
  updatedAt     DateTime         @updatedAt @map("updated_at")
  coachFeedback String?          @map("coach_feedback") // Feedback from the coach to the coachee
  aiSummary     String?          @map("ai_summary") // AI-generated summary of the assignment
  feedbackAt    DateTime?        @map("feedback_at") // Timestamp when feedback was given
  completedAt   DateTime?        @map("completed_at") // Timestamp when the assignment was completed

  // Relations
  exercise     Exercise              @relation(fields: [exerciseId], references: [id], onDelete: Cascade)
  coachee      User                  @relation("AssignedToCoachee", fields: [coacheeId], references: [id])
  coach        User                  @relation("AssignedByCoach", fields: [coachId], references: [id])
  submission   AssignmentSubmission?
  activityLogs ActivityLog[]

  @@map("assignments")
}

model AssignmentSubmission {
  id           String   @id @default(uuid())
  assignmentId String   @unique @map("assignment_id") // Assuming one submission per assignment
  coacheeId    String   @map("coachee_id") // User ID of the submitter
  answers      Json // Store the submitted answers, structure matches Exercise questions
  submittedAt  DateTime @default(now()) @map("submitted_at")

  // Relations
  assignment Assignment @relation(fields: [assignmentId], references: [id], onDelete: Cascade)
  coachee    User       @relation("SubmissionByCoachee", fields: [coacheeId], references: [id], onDelete: Cascade)

  @@map("assignment_submissions")
}

enum AssignmentStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  OVERDUE
}

//--------------------------------------
//       Visualization Models
//--------------------------------------
model Visualization {
  id          String   @id @default(uuid())
  title       String
  description String?
  audioUrl    String?  @map("audio_url") // Assuming audio URL might not always be present
  createdBy   String   @map("created_by") // User ID
  createdAt   DateTime @default(now()) @map("created_at")
  updatedBy   String   @map("updated_by") // User ID
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  creator     User                      @relation("CreatedVisualizations", fields: [createdBy], references: [id])
  updater     User                      @relation("UpdatedVisualizations", fields: [updatedBy], references: [id])
  assignments VisualizationAssignment[]

  @@map("visualizations")
}

model VisualizationAssignment {
  id              String                        @id @default(uuid())
  visualizationId String                        @map("visualization_id")
  coacheeId       String                        @map("coachee_id") // User ID of the coachee
  coachId         String                        @map("coach_id") // User ID of the coach who assigned it
  status          VisualizationAssignmentStatus @default(PENDING)
  dueDate         DateTime?                     @map("due_date")
  createdAt       DateTime                      @default(now()) @map("created_at")
  updatedAt       DateTime                      @updatedAt @map("updated_at")
  // Could add completion timestamp later

  // Relations
  visualization Visualization @relation(fields: [visualizationId], references: [id], onDelete: Cascade)
  coachee       User          @relation("VisAssignedToCoachee", fields: [coacheeId], references: [id])
  coach         User          @relation("VisAssignedByCoach", fields: [coachId], references: [id])
  activityLogs  ActivityLog[]

  @@map("visualization_assignments")
}

enum VisualizationAssignmentStatus {
  PENDING
  VIEWED // Changed from IN_PROGRESS as it's less interactive than an exercise
  COMPLETED
  OVERDUE
}

//--------------------------------------
//       Activity Log Models
//--------------------------------------
model ActivityLog {
  id                        String            @id @default(uuid())
  coachId                   String?           @map("coach_id") // User ID of the coach (nullable for system events)
  coacheeId                 String?           @map("coachee_id") // User ID of the coachee (nullable for coach-only events)
  assignmentId              String?           @map("assignment_id") // Assignment ID (nullable)
  visualizationAssignmentId String?           @map("visualization_assignment_id") // VisualizationAssignment ID (nullable)
  eventType                 ActivityEventType @map("event_type")
  eventMessage              String            @map("event_message")
  timestamp                 DateTime          @default(now())

  // Relations
  coach                   User?                    @relation("ActivityLogCoach", fields: [coachId], references: [id], onDelete: SetNull)
  coachee                 User?                    @relation("ActivityLogCoachee", fields: [coacheeId], references: [id], onDelete: SetNull)
  assignment              Assignment?              @relation(fields: [assignmentId], references: [id], onDelete: SetNull)
  visualizationAssignment VisualizationAssignment? @relation(fields: [visualizationAssignmentId], references: [id], onDelete: SetNull)
  visualizationId         String?

  @@map("activity_logs")
}

enum ActivityEventType {
  EXERCISE_ASSIGNED
  EXERCISE_COMPLETED
  VISUALIZATION_ASSIGNED
  VISUALIZATION_COMPLETED
  COACH_ASSIGNED
  FEEDBACK_PROVIDED
  EVALUATION_CREATED
  EVALUATION_UPDATED
  PERFORMANCE_PROFILE_CREATED
  PERFORMANCE_PROFILE_UPDATED
  PERFORMANCE_PROFILE_DELETED
}

//--------------------------------------
//       B3-5C Evaluation Models
//--------------------------------------
model CoacheeEvaluation {
  id        String   @id @default(uuid())
  coacheeId String   @map("coachee_id") // User ID of the coachee being evaluated
  coachId   String   @map("coach_id") // User ID of the coach creating the evaluation
  title     String? // Optional title for the evaluation (e.g., "Q1 2024 Assessment")
  notes     String? // General notes about the evaluation
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // B3-5C Mental Toughness Scores (1-10 scale)
  composure     Int // Ability to remain calm under pressure
  concentration Int // Ability to focus and maintain attention
  confidence    Int // Self-belief and positive mindset
  copeability   Int // Ability to handle stress and adversity
  cohesion      Int // Ability to work well with others and maintain team unity

  // Relations
  coachee User @relation("EvaluationCoachee", fields: [coacheeId], references: [id], onDelete: Cascade)
  coach   User @relation("EvaluationCreatedBy", fields: [coachId], references: [id], onDelete: Cascade)

  @@map("coachee_evaluations")
}

//--------------------------------------
//       Ideal Performance State Models
//--------------------------------------
model IdealPerformanceState {
  id                  String   @id @default(uuid())
  coacheeId           String   @map("coachee_id") // User ID of the coachee
  dateTime            DateTime @map("date_time") // When the record was created
  competitionDateTime DateTime @map("competition_date_time") // When the competition took place
  competitionName     String   @map("competition_name") // Name of the competition
  performanceScore    Int      @map("performance_score") // Performance score (0-10)
  arousalScore        Int      @map("arousal_score") // Arousal score (0-10)
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  // Relations
  coachee User @relation("IPSCoachee", fields: [coacheeId], references: [id], onDelete: Cascade)

  @@map("ideal_performance_states")
}

//--------------------------------------
//       Performance Profiling Models
//--------------------------------------
model PerformanceProfile {
  id         String   @id @default(uuid())
  coacheeId  String   @map("coachee_id") // User ID of the coachee
  startDate  DateTime @map("start_date") // Start date for the performance profile period
  targetDate DateTime @map("target_date") // Target date for achieving the goals
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // Relations
  coachee User                     @relation("ProfileCoachee", fields: [coacheeId], references: [id], onDelete: Cascade)
  goals   PerformanceProfileGoal[]

  @@map("performance_profiles")
}

enum PerformanceCategory {
  PHYSICAL
  TECHNICAL
  TACTICAL
  MENTAL
}

model PerformanceProfileGoal {
  id                   String              @id @default(uuid())
  performanceProfileId String              @map("performance_profile_id")
  category             PerformanceCategory
  goalName             String              @map("goal_name")
  currentRating        Int                 @map("current_rating") // 0-10 likert scale
  targetRating         Int                 @map("target_rating") // 0-10 likert scale
  isActive             Boolean             @default(true) @map("is_active")
  createdAt            DateTime            @default(now()) @map("created_at")
  updatedAt            DateTime            @updatedAt @map("updated_at")

  // Relations
  performanceProfile PerformanceProfile @relation(fields: [performanceProfileId], references: [id], onDelete: Cascade)

  @@map("performance_profile_goals")
}
